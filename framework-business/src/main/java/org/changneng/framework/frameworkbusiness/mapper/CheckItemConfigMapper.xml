<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
        <result column="ITEM_SORT" jdbcType="DECIMAL" property="itemSort" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="LEVEL" jdbcType="DECIMAL" property="level" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, ITEM_NAME, PARENT_ID, ITEM_SORT, CREATE_TIME, REMARK
    </sql>
    
    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE ID = #{id,jdbcType=VARCHAR}
    </select>
    
    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 根据父级ID查询子项 -->
    <select id="selectByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}
        ORDER BY CREATE_TIME ASC
    </select>
    
    <!-- 根据父级ID查询子项（按排序字段排序） -->
    <select id="selectByParentIdOrderBySort" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 查询顶级检查项 -->
    <select id="selectTopLevelItems" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID IS NULL OR PARENT_ID = ''
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 根据检查项名称模糊查询 -->
    <select id="selectByItemNameLike" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE ITEM_NAME LIKE CONCAT('%', #{itemName,jdbcType=VARCHAR}, '%')
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 获取指定父级ID下的最大排序值 -->
    <select id="getMaxSortByParentId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT MAX(ITEM_SORT)
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}
    </select>
    
    <!-- 统计指定父级ID下的子项数量 -->
    <select id="countByParentId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}
    </select>
    
    <!-- 根据ID列表查询 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 查询完整的树形结构数据（使用Oracle递归查询） -->
    <select id="selectTreeStructure" resultMap="BaseResultMap">
        SELECT 
            ID, 
            ITEM_NAME, 
            PARENT_ID, 
            ITEM_SORT, 
            CREATE_TIME, 
            REMARK,
            LEVEL
        FROM CHECK_ITEM_CONFIG
        START WITH PARENT_ID IS NULL OR PARENT_ID = ''
        CONNECT BY PRIOR ID = PARENT_ID
        ORDER SIBLINGS BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 根据父级ID查询树形结构数据 -->
    <select id="selectTreeStructureByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
            ID, 
            ITEM_NAME, 
            PARENT_ID, 
            ITEM_SORT, 
            CREATE_TIME, 
            REMARK,
            LEVEL
        FROM CHECK_ITEM_CONFIG
        <choose>
            <when test="parentId != null and parentId != ''">
                START WITH ID = #{parentId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                START WITH PARENT_ID IS NULL OR PARENT_ID = ''
            </otherwise>
        </choose>
        CONNECT BY PRIOR ID = PARENT_ID
        ORDER SIBLINGS BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>
    
    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM CHECK_ITEM_CONFIG
        WHERE ID = #{id,jdbcType=VARCHAR}
    </delete>
    
    <!-- 根据父级ID删除 -->
    <delete id="deleteByParentId" parameterType="java.lang.String">
        DELETE FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}
    </delete>
    
    <!-- 根据ID列表批量删除 -->
    <delete id="deleteByIds">
        DELETE FROM CHECK_ITEM_CONFIG
        WHERE ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <!-- 插入记录（所有字段） -->
    <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        INSERT INTO CHECK_ITEM_CONFIG (
            ID, ITEM_NAME, PARENT_ID, ITEM_SORT, CREATE_TIME, REMARK
        ) VALUES (
            #{id,jdbcType=VARCHAR}, 
            #{itemName,jdbcType=VARCHAR}, 
            #{parentId,jdbcType=VARCHAR}, 
            #{itemSort,jdbcType=DECIMAL}, 
            #{createTime,jdbcType=TIMESTAMP}, 
            #{remark,jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        <selectKey keyProperty="id" order="BEFORE" resultType="String">
            SELECT SYS_GUID() FROM DUAL
        </selectKey>
        INSERT INTO CHECK_ITEM_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="itemName != null">
                ITEM_NAME,
            </if>
            <if test="parentId != null">
                PARENT_ID,
            </if>
            <if test="itemSort != null">
                ITEM_SORT,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="itemName != null">
                #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="itemSort != null">
                #{itemSort,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    
    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO CHECK_ITEM_CONFIG (ID, ITEM_NAME, PARENT_ID, ITEM_SORT, CREATE_TIME, REMARK)
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT 
                #{item.id,jdbcType=VARCHAR},
                #{item.itemName,jdbcType=VARCHAR},
                #{item.parentId,jdbcType=VARCHAR},
                #{item.itemSort,jdbcType=DECIMAL},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.remark,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>
    
    <!-- 选择性更新记录 -->
    <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        UPDATE CHECK_ITEM_CONFIG
        <set>
            <if test="itemName != null">
                ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                PARENT_ID = #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="itemSort != null">
                ITEM_SORT = #{itemSort,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 更新记录（所有字段） -->
    <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        UPDATE CHECK_ITEM_CONFIG
        SET ITEM_NAME = #{itemName,jdbcType=VARCHAR},
            PARENT_ID = #{parentId,jdbcType=VARCHAR},
            ITEM_SORT = #{itemSort,jdbcType=DECIMAL},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            REMARK = #{remark,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>
    
    <!-- 更新排序值 -->
    <update id="updateSortById">
        UPDATE CHECK_ITEM_CONFIG
        SET ITEM_SORT = #{itemSort,jdbcType=DECIMAL}
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>
    
</mapper>
