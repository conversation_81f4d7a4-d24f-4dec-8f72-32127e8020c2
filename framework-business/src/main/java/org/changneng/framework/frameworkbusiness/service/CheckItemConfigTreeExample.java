package org.changneng.framework.frameworkbusiness.service;

import java.util.Date;
import java.util.List;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;

/**
 * 检查项配置树形结构使用示例
 *
 * 这个类展示了如何使用CheckItemConfigService的树形结构功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public class CheckItemConfigTreeExample {

    /**
     * 示例：获取并打印树形结构
     *
     * 注意：此示例假设数据库中已经存在检查项配置数据
     * 树形结构示例：
     * 环境保护检查
     * ├── 大气污染防治
     * │   ├── 废气排放检查
     * │   └── 烟尘排放检查
     * ├── 水污染防治
     * │   ├── 废水排放检查
     * │   └── 水质监测检查
     * └── 固体废物管理
     *     ├── 危险废物处置
     *     └── 一般废物处理
     */

    /**
     * 示例：获取并打印树形结构
     */
    public void printTreeStructure(CheckItemConfigService checkItemConfigService) {
        System.out.println("=== 获取完整树形结构 ===");
        List<CheckItemConfigTreeVO> treeStructure = checkItemConfigService.getTreeStructure();
        printTree(treeStructure, 0);

        System.out.println("\n=== 验证树形结构 ===");
        var validationResult = checkItemConfigService.validateTreeStructure(treeStructure);
        System.out.println("验证结果: " + validationResult.getMeta().getResult());
        System.out.println("验证消息: " + validationResult.getMeta().getMessage());
    }

    /**
     * 递归打印树形结构
     */
    private void printTree(List<CheckItemConfigTreeVO> nodes, int level) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        for (CheckItemConfigTreeVO node : nodes) {
            // 打印缩进
            for (int i = 0; i < level; i++) {
                System.out.print("  ");
            }

            // 打印节点信息
            System.out.println("├── " + node.getItemName() +
                " (ID: " + node.getId() +
                ", Sort: " + node.getItemSort() +
                ", Level: " + node.getLevel() +
                ", Path: " + node.getNodePath() +
                ", HasChildren: " + node.getHasChildren() +
                ", IsLeaf: " + node.getIsLeaf() + ")");

            // 递归打印子节点
            printTree(node.getChildren(), level + 1);
        }
    }

    /**
     * 示例：根据父级ID获取子树
     */
    public void printSubTree(CheckItemConfigService checkItemConfigService, String parentId) {
        System.out.println("=== 获取子树结构 (父级ID: " + parentId + ") ===");
        List<CheckItemConfigTreeVO> subTree = checkItemConfigService.getTreeStructureByParentId(parentId);
        printTree(subTree, 0);
    }

    /**
     * 示例：展示TreeVO的前端展示属性
     */
    public void demonstrateTreeVOFeatures(CheckItemConfigService checkItemConfigService) {
        System.out.println("=== TreeVO特性演示 ===");
        List<CheckItemConfigTreeVO> treeStructure = checkItemConfigService.getTreeStructure();

        for (CheckItemConfigTreeVO node : treeStructure) {
            System.out.println("节点: " + node.getItemName());
            System.out.println("  - 节点路径: " + node.getNodePath());
            System.out.println("  - 层级深度: " + node.getLevel());
            System.out.println("  - 是否有子节点: " + node.getHasChildren());
            System.out.println("  - 是否为叶子节点: " + node.getIsLeaf());
            System.out.println("  - 是否展开: " + node.getExpanded());
            System.out.println("  - 子节点数量: " + node.getChildren().size());
            System.out.println();
        }
    }
}
