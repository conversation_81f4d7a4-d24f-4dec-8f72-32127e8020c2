package org.changneng.framework.frameworkbusiness.service;

import java.util.Date;
import java.util.List;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;

/**
 * 检查项配置树形结构使用示例
 * 
 * 这个类展示了如何使用CheckItemConfigService的树形结构功能
 * 
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public class CheckItemConfigTreeExample {
    
    /**
     * 示例：创建树形结构数据
     * 
     * 树形结构示例：
     * 环境保护检查
     * ├── 大气污染防治
     * │   ├── 废气排放检查
     * │   └── 烟尘排放检查
     * ├── 水污染防治
     * │   ├── 废水排放检查
     * │   └── 水质监测检查
     * └── 固体废物管理
     *     ├── 危险废物处置
     *     └── 一般废物处理
     */
    public void createSampleTreeData(CheckItemConfigService checkItemConfigService) {
        Date now = new Date();
        
        // 1. 创建顶级节点
        CheckItemConfig root = new CheckItemConfig();
        root.setItemName("环境保护检查");
        root.setParentId(null);
        root.setItemSort(1);
        root.setCreateTime(now);
        root.setRemark("环境保护综合检查项目");
        checkItemConfigService.insertSelective(root);
        
        // 2. 创建二级节点
        CheckItemConfig air = new CheckItemConfig();
        air.setItemName("大气污染防治");
        air.setParentId(root.getId());
        air.setItemSort(1);
        air.setCreateTime(now);
        air.setRemark("大气污染防治相关检查");
        checkItemConfigService.insertSelective(air);
        
        CheckItemConfig water = new CheckItemConfig();
        water.setItemName("水污染防治");
        water.setParentId(root.getId());
        water.setItemSort(2);
        water.setCreateTime(now);
        water.setRemark("水污染防治相关检查");
        checkItemConfigService.insertSelective(water);
        
        CheckItemConfig waste = new CheckItemConfig();
        waste.setItemName("固体废物管理");
        waste.setParentId(root.getId());
        waste.setItemSort(3);
        waste.setCreateTime(now);
        waste.setRemark("固体废物管理相关检查");
        checkItemConfigService.insertSelective(waste);
        
        // 3. 创建三级节点（大气污染防治的子项）
        CheckItemConfig airEmission = new CheckItemConfig();
        airEmission.setItemName("废气排放检查");
        airEmission.setParentId(air.getId());
        airEmission.setItemSort(1);
        airEmission.setCreateTime(now);
        airEmission.setRemark("检查企业废气排放是否达标");
        checkItemConfigService.insertSelective(airEmission);
        
        CheckItemConfig dustEmission = new CheckItemConfig();
        dustEmission.setItemName("烟尘排放检查");
        dustEmission.setParentId(air.getId());
        dustEmission.setItemSort(2);
        dustEmission.setCreateTime(now);
        dustEmission.setRemark("检查烟尘排放浓度和总量");
        checkItemConfigService.insertSelective(dustEmission);
        
        // 4. 创建三级节点（水污染防治的子项）
        CheckItemConfig waterEmission = new CheckItemConfig();
        waterEmission.setItemName("废水排放检查");
        waterEmission.setParentId(water.getId());
        waterEmission.setItemSort(1);
        waterEmission.setCreateTime(now);
        waterEmission.setRemark("检查企业废水排放是否达标");
        checkItemConfigService.insertSelective(waterEmission);
        
        CheckItemConfig waterQuality = new CheckItemConfig();
        waterQuality.setItemName("水质监测检查");
        waterQuality.setParentId(water.getId());
        waterQuality.setItemSort(2);
        waterQuality.setCreateTime(now);
        waterQuality.setRemark("检查水质监测设备和数据");
        checkItemConfigService.insertSelective(waterQuality);
        
        // 5. 创建三级节点（固体废物管理的子项）
        CheckItemConfig hazardousWaste = new CheckItemConfig();
        hazardousWaste.setItemName("危险废物处置");
        hazardousWaste.setParentId(waste.getId());
        hazardousWaste.setItemSort(1);
        hazardousWaste.setCreateTime(now);
        hazardousWaste.setRemark("检查危险废物的收集、储存、处置");
        checkItemConfigService.insertSelective(hazardousWaste);
        
        CheckItemConfig generalWaste = new CheckItemConfig();
        generalWaste.setItemName("一般废物处理");
        generalWaste.setParentId(waste.getId());
        generalWaste.setItemSort(2);
        generalWaste.setCreateTime(now);
        generalWaste.setRemark("检查一般固体废物的处理情况");
        checkItemConfigService.insertSelective(generalWaste);
    }
    
    /**
     * 示例：获取并打印树形结构
     */
    public void printTreeStructure(CheckItemConfigService checkItemConfigService) {
        System.out.println("=== 获取完整树形结构 ===");
        List<CheckItemConfig> treeStructure = checkItemConfigService.getTreeStructure();
        printTree(treeStructure, 0);
        
        System.out.println("\n=== 验证树形结构 ===");
        var validationResult = checkItemConfigService.validateTreeStructure(treeStructure);
        System.out.println("验证结果: " + validationResult.getMeta().getResult());
        System.out.println("验证消息: " + validationResult.getMeta().getMessage());
    }
    
    /**
     * 递归打印树形结构
     */
    private void printTree(List<CheckItemConfig> nodes, int level) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        for (CheckItemConfig node : nodes) {
            // 打印缩进
            for (int i = 0; i < level; i++) {
                System.out.print("  ");
            }
            
            // 打印节点信息
            System.out.println("├── " + node.getItemName() + 
                " (ID: " + node.getId() + 
                ", Sort: " + node.getItemSort() + 
                ", Level: " + node.getLevel() + ")");
            
            // 递归打印子节点
            printTree(node.getChildren(), level + 1);
        }
    }
    
    /**
     * 示例：根据父级ID获取子树
     */
    public void printSubTree(CheckItemConfigService checkItemConfigService, String parentId) {
        System.out.println("=== 获取子树结构 (父级ID: " + parentId + ") ===");
        List<CheckItemConfig> subTree = checkItemConfigService.getTreeStructureByParentId(parentId);
        printTree(subTree, 0);
    }
    
    /**
     * 示例：搜索功能
     */
    public void searchExample(CheckItemConfigService checkItemConfigService) {
        System.out.println("=== 搜索示例 ===");
        
        // 模糊搜索
        List<CheckItemConfig> searchResults = checkItemConfigService.selectByItemNameLike("废气");
        System.out.println("搜索'废气'的结果:");
        for (CheckItemConfig item : searchResults) {
            System.out.println("- " + item.getItemName() + " (ID: " + item.getId() + ")");
        }
        
        // 获取顶级项目
        List<CheckItemConfig> topLevelItems = checkItemConfigService.selectTopLevelItems();
        System.out.println("\n顶级检查项:");
        for (CheckItemConfig item : topLevelItems) {
            System.out.println("- " + item.getItemName() + " (ID: " + item.getId() + ")");
        }
    }
}
