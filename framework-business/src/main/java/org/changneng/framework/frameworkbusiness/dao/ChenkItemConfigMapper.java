package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.ChenkItemConfig;

/**
 * 环境监管一件事-检查项配置 Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface ChenkItemConfigMapper {
    
    /**
     * 根据主键删除记录
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(String id);
    
    /**
     * 插入记录（所有字段）
     * 
     * @param record 检查项配置对象
     * @return 影响行数
     */
    int insert(ChenkItemConfig record);
    
    /**
     * 选择性插入记录（非空字段）
     * 
     * @param record 检查项配置对象
     * @return 影响行数
     */
    int insertSelective(ChenkItemConfig record);
    
    /**
     * 根据主键查询记录
     * 
     * @param id 主键ID
     * @return 检查项配置对象
     */
    ChenkItemConfig selectByPrimaryKey(String id);
    
    /**
     * 选择性更新记录（非空字段）
     * 
     * @param record 检查项配置对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ChenkItemConfig record);
    
    /**
     * 根据主键更新记录（所有字段）
     * 
     * @param record 检查项配置对象
     * @return 影响行数
     */
    int updateByPrimaryKey(ChenkItemConfig record);
    
    /**
     * 查询所有检查项配置
     * 
     * @return 检查项配置列表
     */
    List<ChenkItemConfig> selectAll();
    
    /**
     * 根据父级ID查询子项列表
     * 
     * @param parentId 父级ID
     * @return 检查项配置列表
     */
    List<ChenkItemConfig> selectByParentId(@Param("parentId") String parentId);
    
    /**
     * 根据父级ID查询子项列表（按排序字段排序）
     * 
     * @param parentId 父级ID
     * @return 检查项配置列表
     */
    List<ChenkItemConfig> selectByParentIdOrderBySort(@Param("parentId") String parentId);
    
    /**
     * 查询顶级检查项（父级ID为空或null）
     * 
     * @return 检查项配置列表
     */
    List<ChenkItemConfig> selectTopLevelItems();
    
    /**
     * 根据检查项名称模糊查询
     * 
     * @param itemName 检查项名称
     * @return 检查项配置列表
     */
    List<ChenkItemConfig> selectByItemNameLike(@Param("itemName") String itemName);
    
    /**
     * 获取指定父级ID下的最大排序值
     * 
     * @param parentId 父级ID
     * @return 最大排序值
     */
    Integer getMaxSortByParentId(@Param("parentId") String parentId);
    
    /**
     * 批量插入检查项配置
     * 
     * @param list 检查项配置列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<ChenkItemConfig> list);
    
    /**
     * 根据父级ID批量删除
     * 
     * @param parentId 父级ID
     * @return 影响行数
     */
    int deleteByParentId(@Param("parentId") String parentId);
    
    /**
     * 统计指定父级ID下的子项数量
     * 
     * @param parentId 父级ID
     * @return 子项数量
     */
    int countByParentId(@Param("parentId") String parentId);
    
    /**
     * 更新排序值
     * 
     * @param id 主键ID
     * @param itemSort 新的排序值
     * @return 影响行数
     */
    int updateSortById(@Param("id") String id, @Param("itemSort") Integer itemSort);
    
    /**
     * 根据ID列表查询检查项配置
     * 
     * @param ids ID列表
     * @return 检查项配置列表
     */
    List<ChenkItemConfig> selectByIds(@Param("ids") List<String> ids);
    
    /**
     * 根据ID列表批量删除
     * 
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<String> ids);
}
