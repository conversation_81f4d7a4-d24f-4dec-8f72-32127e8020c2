package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;

/**
 * 环境监管一件事-检查项配置 Mapper接口
 * 专注于树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface CheckItemConfigMapper {

    /**
     * 查询完整的树形结构数据（使用Oracle递归查询）
     * 使用Oracle的CONNECT BY语法进行递归查询，返回按层级排序的平铺数据
     *
     * @return 树形结构的检查项配置列表，包含层级信息
     */
    List<CheckItemConfig> selectTreeStructure();

    /**
     * 根据父级ID查询树形结构数据
     * 如果parentId为null或空字符串，则查询所有顶级节点的树形结构
     * 如果parentId有值，则查询以该节点为根的子树结构
     *
     * @param parentId 父级ID，如果为null则查询所有顶级节点的树形结构
     * @return 树形结构的检查项配置列表，包含层级信息
     */
    List<CheckItemConfig> selectTreeStructureByParentId(@Param("parentId") String parentId);
}
