package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;
import org.changneng.framework.frameworkbusiness.service.CheckItemConfigService;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;

/**
 * 环境监管一件事-检查项配置 Service实现类
 * 
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
@Service
@Transactional
public class CheckItemConfigServiceImpl implements CheckItemConfigService {
    
    private static final Logger logger = LogManager.getLogger(CheckItemConfigServiceImpl.class);
    
    @Autowired
    private CheckItemConfigMapper checkItemConfigMapper;
    
    @Override
    public CheckItemConfig selectByPrimaryKey(String id) {
        if (ChangnengUtil.isNull(id)) {
            return null;
        }
        return checkItemConfigMapper.selectByPrimaryKey(id);
    }
    
    @Override
    public List<CheckItemConfig> selectAll() {
        return checkItemConfigMapper.selectAll();
    }
    
    @Override
    public List<CheckItemConfig> selectByParentId(String parentId) {
        return checkItemConfigMapper.selectByParentIdOrderBySort(parentId);
    }
    
    @Override
    public List<CheckItemConfig> selectTopLevelItems() {
        return checkItemConfigMapper.selectTopLevelItems();
    }
    
    @Override
    public List<CheckItemConfig> selectByItemNameLike(String itemName) {
        if (ChangnengUtil.isNull(itemName)) {
            return new ArrayList<>();
        }
        return checkItemConfigMapper.selectByItemNameLike(itemName);
    }
    
    @Override
    public ResponseJson insert(CheckItemConfig checkItemConfig) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (checkItemConfig == null) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "检查项配置对象不能为空", "检查项配置对象不能为空");
            }
            
            // 设置创建时间
            if (checkItemConfig.getCreateTime() == null) {
                checkItemConfig.setCreateTime(new Date());
            }
            
            // 如果没有设置排序值，则自动设置为最大值+1
            if (checkItemConfig.getItemSort() == null) {
                Integer maxSort = getMaxSortByParentId(checkItemConfig.getParentId());
                checkItemConfig.setItemSort(maxSort == null ? 1 : maxSort + 1);
            }
            
            int result = checkItemConfigMapper.insert(checkItemConfig);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.SAVE_SUCCESS.toString(), "新增检查项配置成功", "新增检查项配置成功", checkItemConfig);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.SAVE_FAIL.toString(), "新增检查项配置失败", "新增检查项配置失败");
            }
        } catch (Exception e) {
            logger.error("新增检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.SAVE_FAIL.toString(), "新增检查项配置失败：" + e.getMessage(), "新增检查项配置失败");
        }
    }
    
    @Override
    public ResponseJson insertSelective(CheckItemConfig checkItemConfig) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (checkItemConfig == null) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "检查项配置对象不能为空", "检查项配置对象不能为空");
            }
            
            // 设置创建时间
            if (checkItemConfig.getCreateTime() == null) {
                checkItemConfig.setCreateTime(new Date());
            }
            
            // 如果没有设置排序值，则自动设置为最大值+1
            if (checkItemConfig.getItemSort() == null) {
                Integer maxSort = getMaxSortByParentId(checkItemConfig.getParentId());
                checkItemConfig.setItemSort(maxSort == null ? 1 : maxSort + 1);
            }
            
            int result = checkItemConfigMapper.insertSelective(checkItemConfig);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.SAVE_SUCCESS.toString(), "新增检查项配置成功", "新增检查项配置成功", checkItemConfig);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.SAVE_FAIL.toString(), "新增检查项配置失败", "新增检查项配置失败");
            }
        } catch (Exception e) {
            logger.error("新增检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.SAVE_FAIL.toString(), "新增检查项配置失败：" + e.getMessage(), "新增检查项配置失败");
        }
    }
    
    @Override
    public ResponseJson updateByPrimaryKey(CheckItemConfig checkItemConfig) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (checkItemConfig == null || ChangnengUtil.isNull(checkItemConfig.getId())) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "检查项配置对象或ID不能为空", "检查项配置对象或ID不能为空");
            }
            
            int result = checkItemConfigMapper.updateByPrimaryKey(checkItemConfig);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.UPDATE_SUCCESS.toString(), "更新检查项配置成功", "更新检查项配置成功", checkItemConfig);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.UPDATE_FAIL.toString(), "更新检查项配置失败", "更新检查项配置失败");
            }
        } catch (Exception e) {
            logger.error("更新检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.UPDATE_FAIL.toString(), "更新检查项配置失败：" + e.getMessage(), "更新检查项配置失败");
        }
    }
    
    @Override
    public ResponseJson updateByPrimaryKeySelective(CheckItemConfig checkItemConfig) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (checkItemConfig == null || ChangnengUtil.isNull(checkItemConfig.getId())) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "检查项配置对象或ID不能为空", "检查项配置对象或ID不能为空");
            }
            
            int result = checkItemConfigMapper.updateByPrimaryKeySelective(checkItemConfig);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.UPDATE_SUCCESS.toString(), "更新检查项配置成功", "更新检查项配置成功", checkItemConfig);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.UPDATE_FAIL.toString(), "更新检查项配置失败", "更新检查项配置失败");
            }
        } catch (Exception e) {
            logger.error("更新检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.UPDATE_FAIL.toString(), "更新检查项配置失败：" + e.getMessage(), "更新检查项配置失败");
        }
    }
    
    @Override
    public ResponseJson deleteByPrimaryKey(String id) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (ChangnengUtil.isNull(id)) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "ID不能为空", "ID不能为空");
            }
            
            // 检查是否有子节点
            int childCount = checkItemConfigMapper.countByParentId(id);
            if (childCount > 0) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.DELETE_FAIL.toString(), "该节点下存在子节点，无法删除", "该节点下存在子节点，无法删除");
            }
            
            int result = checkItemConfigMapper.deleteByPrimaryKey(id);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.DELETE_SUCCESS.toString(), "删除检查项配置成功", "删除检查项配置成功", null);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.DELETE_FAIL.toString(), "删除检查项配置失败", "删除检查项配置失败");
            }
        } catch (Exception e) {
            logger.error("删除检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.DELETE_FAIL.toString(), "删除检查项配置失败：" + e.getMessage(), "删除检查项配置失败");
        }
    }
    
    @Override
    public ResponseJson deleteByParentId(String parentId) {
        ResponseJson responseJson = new ResponseJson();
        try {
            int result = checkItemConfigMapper.deleteByParentId(parentId);
            return responseJson.success(HttpStatus.OK.toString(), 
                SystemStatusCode.DELETE_SUCCESS.toString(), "批量删除检查项配置成功", "批量删除检查项配置成功", result);
        } catch (Exception e) {
            logger.error("批量删除检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.DELETE_FAIL.toString(), "批量删除检查项配置失败：" + e.getMessage(), "批量删除检查项配置失败");
        }
    }
    
    @Override
    public ResponseJson batchInsert(List<CheckItemConfig> list) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (list == null || list.isEmpty()) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "批量插入列表不能为空", "批量插入列表不能为空");
            }
            
            // 为每个对象设置创建时间和ID
            Date now = new Date();
            for (CheckItemConfig item : list) {
                if (item.getCreateTime() == null) {
                    item.setCreateTime(now);
                }
                if (ChangnengUtil.isNull(item.getId())) {
                    item.setId(UUID.randomUUID().toString().replace("-", ""));
                }
            }
            
            int result = checkItemConfigMapper.batchInsert(list);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.SAVE_SUCCESS.toString(), "批量新增检查项配置成功", "批量新增检查项配置成功", result);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.SAVE_FAIL.toString(), "批量新增检查项配置失败", "批量新增检查项配置失败");
            }
        } catch (Exception e) {
            logger.error("批量新增检查项配置失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.SAVE_FAIL.toString(), "批量新增检查项配置失败：" + e.getMessage(), "批量新增检查项配置失败");
        }
    }
    
    @Override
    public Integer getMaxSortByParentId(String parentId) {
        return checkItemConfigMapper.getMaxSortByParentId(parentId);
    }
    
    @Override
    public ResponseJson updateSortById(String id, Integer itemSort) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (ChangnengUtil.isNull(id) || itemSort == null) {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "ID和排序值不能为空", "ID和排序值不能为空");
            }
            
            int result = checkItemConfigMapper.updateSortById(id, itemSort);
            if (result > 0) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.UPDATE_SUCCESS.toString(), "更新排序值成功", "更新排序值成功", null);
            } else {
                return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                    SystemStatusCode.UPDATE_FAIL.toString(), "更新排序值失败", "更新排序值失败");
            }
        } catch (Exception e) {
            logger.error("更新排序值失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
                SystemStatusCode.UPDATE_FAIL.toString(), "更新排序值失败：" + e.getMessage(), "更新排序值失败");
        }
    }

    /**
     * 获取完整的树形结构数据
     * 核心的树形结构查询方法，将平铺的数据转换为嵌套的树形结构
     */
    @Override
    public List<CheckItemConfig> getTreeStructure() {
        try {
            // 方法1：使用Oracle的递归查询（推荐，性能更好）
            List<CheckItemConfig> treeList = checkItemConfigMapper.selectTreeStructure();
            if (treeList != null && !treeList.isEmpty()) {
                return buildTreeStructureFromHierarchicalData(treeList);
            }

            // 方法2：如果递归查询不可用，则使用Java构建树形结构
            List<CheckItemConfig> allItems = checkItemConfigMapper.selectAll();
            return buildTreeStructure(allItems);

        } catch (Exception e) {
            logger.error("获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据父级ID获取树形结构数据
     */
    @Override
    public List<CheckItemConfig> getTreeStructureByParentId(String parentId) {
        try {
            List<CheckItemConfig> treeList = checkItemConfigMapper.selectTreeStructureByParentId(parentId);
            if (treeList != null && !treeList.isEmpty()) {
                return buildTreeStructureFromHierarchicalData(treeList);
            }

            // 如果递归查询不可用，则使用Java构建树形结构
            List<CheckItemConfig> allItems;
            if (ChangnengUtil.isNull(parentId)) {
                allItems = checkItemConfigMapper.selectAll();
            } else {
                allItems = getAllChildrenRecursively(parentId);
            }
            return buildTreeStructure(allItems);

        } catch (Exception e) {
            logger.error("根据父级ID获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建树形结构（从平铺数据转换为树形结构）
     * 这是一个工具方法，用于将平铺的检查项配置列表转换为树形结构
     */
    @Override
    public List<CheckItemConfig> buildTreeStructure(List<CheckItemConfig> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建ID到对象的映射，便于快速查找
        Map<String, CheckItemConfig> itemMap = flatList.stream()
            .collect(Collectors.toMap(CheckItemConfig::getId, item -> item));

        // 找出所有顶级节点（父级ID为空或null）
        List<CheckItemConfig> rootNodes = new ArrayList<>();

        for (CheckItemConfig item : flatList) {
            String parentId = item.getParentId();

            if (ChangnengUtil.isNull(parentId)) {
                // 顶级节点
                rootNodes.add(item);
            } else {
                // 子节点，找到其父节点并添加到父节点的children列表中
                CheckItemConfig parent = itemMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(item);
                    // 设置层级深度
                    item.setLevel(parent.getLevel() == null ? 1 : parent.getLevel() + 1);
                } else {
                    // 如果找不到父节点，可能是数据不完整，将其作为顶级节点处理
                    logger.warn("找不到父节点，ID: " + parentId + "，将节点 " + item.getId() + " 作为顶级节点处理");
                    rootNodes.add(item);
                }
            }
        }

        // 为顶级节点设置层级深度
        for (CheckItemConfig rootNode : rootNodes) {
            if (rootNode.getLevel() == null) {
                rootNode.setLevel(0);
                setChildrenLevel(rootNode, 0);
            }
        }

        // 对每个节点的子节点按排序字段排序
        sortChildrenRecursively(rootNodes);

        return rootNodes;
    }

    /**
     * 验证树形结构的完整性
     * 检查是否存在循环引用、孤立节点等问题
     */
    @Override
    public ResponseJson validateTreeStructure(List<CheckItemConfig> treeList) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (treeList == null || treeList.isEmpty()) {
                return responseJson.success(HttpStatus.OK.toString(),
                    SystemStatusCode.QUERY_SUCCESS.toString(), "树形结构验证通过", "树形结构验证通过", null);
            }

            Set<String> visitedIds = new HashSet<>();
            List<String> issues = new ArrayList<>();

            // 递归验证每个节点
            for (CheckItemConfig node : treeList) {
                validateNodeRecursively(node, visitedIds, new HashSet<>(), issues);
            }

            if (issues.isEmpty()) {
                return responseJson.success(HttpStatus.OK.toString(),
                    SystemStatusCode.QUERY_SUCCESS.toString(), "树形结构验证通过", "树形结构验证通过", null);
            } else {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(),
                    SystemStatusCode.PARAM_ERROR.toString(), "树形结构验证失败", "树形结构验证失败", issues);
            }

        } catch (Exception e) {
            logger.error("验证树形结构失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
                SystemStatusCode.QUERY_FAIL.toString(), "验证树形结构失败：" + e.getMessage(), "验证树形结构失败");
        }
    }

    /**
     * 从Oracle递归查询结果构建树形结构
     * Oracle的CONNECT BY查询返回的是按层级排序的平铺数据，需要转换为嵌套结构
     */
    private List<CheckItemConfig> buildTreeStructureFromHierarchicalData(List<CheckItemConfig> hierarchicalList) {
        if (hierarchicalList == null || hierarchicalList.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用栈来构建树形结构
        Stack<CheckItemConfig> stack = new Stack<>();
        List<CheckItemConfig> result = new ArrayList<>();

        for (CheckItemConfig item : hierarchicalList) {
            Integer currentLevel = item.getLevel();
            if (currentLevel == null) {
                currentLevel = 0;
            }

            // 弹出栈中层级大于等于当前层级的节点
            while (!stack.isEmpty() && stack.peek().getLevel() != null && stack.peek().getLevel() >= currentLevel) {
                stack.pop();
            }

            if (stack.isEmpty()) {
                // 顶级节点
                result.add(item);
            } else {
                // 子节点，添加到栈顶节点的children中
                stack.peek().getChildren().add(item);
            }

            stack.push(item);
        }

        return result;
    }

    /**
     * 递归获取所有子节点
     */
    private List<CheckItemConfig> getAllChildrenRecursively(String parentId) {
        List<CheckItemConfig> result = new ArrayList<>();
        List<CheckItemConfig> directChildren = checkItemConfigMapper.selectByParentIdOrderBySort(parentId);

        for (CheckItemConfig child : directChildren) {
            result.add(child);
            result.addAll(getAllChildrenRecursively(child.getId()));
        }

        return result;
    }

    /**
     * 递归设置子节点的层级深度
     */
    private void setChildrenLevel(CheckItemConfig parent, int parentLevel) {
        List<CheckItemConfig> children = parent.getChildren();
        if (children != null && !children.isEmpty()) {
            for (CheckItemConfig child : children) {
                child.setLevel(parentLevel + 1);
                setChildrenLevel(child, parentLevel + 1);
            }
        }
    }

    /**
     * 递归对子节点进行排序
     */
    private void sortChildrenRecursively(List<CheckItemConfig> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        // 对当前层级的节点按排序字段排序
        nodes.sort((a, b) -> {
            Integer sortA = a.getItemSort();
            Integer sortB = b.getItemSort();

            if (sortA == null && sortB == null) {
                return 0;
            } else if (sortA == null) {
                return 1;
            } else if (sortB == null) {
                return -1;
            } else {
                int result = sortA.compareTo(sortB);
                if (result == 0) {
                    // 如果排序值相同，则按创建时间排序
                    Date timeA = a.getCreateTime();
                    Date timeB = b.getCreateTime();
                    if (timeA != null && timeB != null) {
                        return timeA.compareTo(timeB);
                    }
                }
                return result;
            }
        });

        // 递归对每个节点的子节点进行排序
        for (CheckItemConfig node : nodes) {
            sortChildrenRecursively(node.getChildren());
        }
    }

    /**
     * 递归验证节点，检查循环引用等问题
     */
    private void validateNodeRecursively(CheckItemConfig node, Set<String> globalVisited,
                                       Set<String> currentPath, List<String> issues) {
        if (node == null || ChangnengUtil.isNull(node.getId())) {
            issues.add("发现空节点或节点ID为空");
            return;
        }

        String nodeId = node.getId();

        // 检查循环引用
        if (currentPath.contains(nodeId)) {
            issues.add("发现循环引用，节点ID: " + nodeId);
            return;
        }

        // 检查重复节点
        if (globalVisited.contains(nodeId)) {
            issues.add("发现重复节点，节点ID: " + nodeId);
            return;
        }

        globalVisited.add(nodeId);
        currentPath.add(nodeId);

        // 递归验证子节点
        List<CheckItemConfig> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            for (CheckItemConfig child : children) {
                validateNodeRecursively(child, globalVisited, new HashSet<>(currentPath), issues);
            }
        }
    }
