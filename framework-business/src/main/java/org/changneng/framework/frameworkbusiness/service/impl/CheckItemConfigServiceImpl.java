package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.*;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkbusiness.service.CheckItemConfigService;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;

/**
 * 环境监管一件事-检查项配置 Service实现类
 * 专注于树形结构查询功能
 * 
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
@Service
@Transactional
public class CheckItemConfigServiceImpl implements CheckItemConfigService {
    
    private static final Logger logger = LogManager.getLogger(CheckItemConfigServiceImpl.class);
    
    @Autowired
    private CheckItemConfigMapper checkItemConfigMapper;
    
    /**
     * 获取完整的树形结构数据
     * 核心的树形结构查询方法，将平铺的数据转换为嵌套的树形结构
     */
    @Override
    public List<CheckItemConfigTreeVO> getTreeStructure() {
        try {
            // 使用Oracle的递归查询获取按层级排序的数据
            List<CheckItemConfig> hierarchicalList = checkItemConfigMapper.selectTreeStructure();
            
            if (hierarchicalList == null || hierarchicalList.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 转换为TreeVO并构建树形结构
            return buildTreeStructureFromHierarchicalData(hierarchicalList);
            
        } catch (Exception e) {
            logger.error("获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据父级ID获取树形结构数据
     */
    @Override
    public List<CheckItemConfigTreeVO> getTreeStructureByParentId(String parentId) {
        try {
            // 使用Oracle的递归查询获取按层级排序的数据
            List<CheckItemConfig> hierarchicalList = checkItemConfigMapper.selectTreeStructureByParentId(parentId);
            
            if (hierarchicalList == null || hierarchicalList.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 转换为TreeVO并构建树形结构
            return buildTreeStructureFromHierarchicalData(hierarchicalList);
            
        } catch (Exception e) {
            logger.error("根据父级ID获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 验证树形结构的完整性
     * 检查是否存在循环引用、孤立节点等问题
     */
    @Override
    public ResponseJson validateTreeStructure(List<CheckItemConfigTreeVO> treeList) {
        ResponseJson responseJson = new ResponseJson();
        try {
            if (treeList == null || treeList.isEmpty()) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.QUERY_SUCCESS.toString(), "树形结构验证通过", "树形结构验证通过", null);
            }
            
            Set<String> visitedIds = new HashSet<>();
            List<String> issues = new ArrayList<>();
            
            // 递归验证每个节点
            for (CheckItemConfigTreeVO node : treeList) {
                validateNodeRecursively(node, visitedIds, new HashSet<>(), issues);
            }
            
            if (issues.isEmpty()) {
                return responseJson.success(HttpStatus.OK.toString(), 
                    SystemStatusCode.QUERY_SUCCESS.toString(), "树形结构验证通过", "树形结构验证通过", null);
            } else {
                return responseJson.error(HttpStatus.BAD_REQUEST.toString(), 
                    SystemStatusCode.PARAM_ERROR.toString(), "树形结构验证失败", "树形结构验证失败", issues);
            }
            
        } catch (Exception e) {
            logger.error("验证树形结构失败", e);
            return responseJson.error(HttpStatus.INTERNAL_SERVER_ERROR.toString(), 
                SystemStatusCode.QUERY_FAIL.toString(), "验证树形结构失败：" + e.getMessage(), "验证树形结构失败");
        }
    }
    
    /**
     * 从Oracle递归查询结果构建树形结构
     * Oracle的CONNECT BY查询返回的是按层级排序的平铺数据，需要转换为嵌套结构
     */
    private List<CheckItemConfigTreeVO> buildTreeStructureFromHierarchicalData(List<CheckItemConfig> hierarchicalList) {
        if (hierarchicalList == null || hierarchicalList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用栈来构建树形结构
        Stack<CheckItemConfigTreeVO> stack = new Stack<>();
        List<CheckItemConfigTreeVO> result = new ArrayList<>();
        
        for (CheckItemConfig item : hierarchicalList) {
            // 转换为TreeVO
            CheckItemConfigTreeVO treeVO = convertToTreeVO(item);
            
            Integer currentLevel = treeVO.getLevel();
            if (currentLevel == null) {
                currentLevel = 0;
                treeVO.setLevel(currentLevel);
            }
            
            // 弹出栈中层级大于等于当前层级的节点
            while (!stack.isEmpty() && stack.peek().getLevel() != null && stack.peek().getLevel() >= currentLevel) {
                stack.pop();
            }
            
            if (stack.isEmpty()) {
                // 顶级节点
                result.add(treeVO);
            } else {
                // 子节点，添加到栈顶节点的children中
                CheckItemConfigTreeVO parent = stack.peek();
                parent.addChild(treeVO);
                
                // 构建节点路径
                String parentPath = parent.getNodePath();
                if (ChangnengUtil.isNull(parentPath)) {
                    treeVO.setNodePath(treeVO.getItemName());
                } else {
                    treeVO.setNodePath(parentPath + "/" + treeVO.getItemName());
                }
            }
            
            stack.push(treeVO);
        }
        
        // 设置根节点的路径
        for (CheckItemConfigTreeVO rootNode : result) {
            if (ChangnengUtil.isNull(rootNode.getNodePath())) {
                rootNode.setNodePath(rootNode.getItemName());
                setChildrenNodePath(rootNode, rootNode.getItemName());
            }
        }
        
        return result;
    }
    
    /**
     * 将CheckItemConfig转换为CheckItemConfigTreeVO
     */
    private CheckItemConfigTreeVO convertToTreeVO(CheckItemConfig entity) {
        if (entity == null) {
            return null;
        }
        
        CheckItemConfigTreeVO treeVO = new CheckItemConfigTreeVO();
        treeVO.setId(entity.getId());
        treeVO.setItemName(entity.getItemName());
        treeVO.setParentId(entity.getParentId());
        treeVO.setItemSort(entity.getItemSort());
        treeVO.setCreateTime(entity.getCreateTime());
        treeVO.setRemark(entity.getRemark());
        treeVO.setLevel(entity.getLevel());
        
        // 设置默认的前端展示属性
        treeVO.setHasChildren(false);
        treeVO.setExpanded(false);
        treeVO.setIsLeaf(true);
        
        return treeVO;
    }
    
    /**
     * 递归设置子节点的路径
     */
    private void setChildrenNodePath(CheckItemConfigTreeVO parent, String parentPath) {
        List<CheckItemConfigTreeVO> children = parent.getChildren();
        if (children != null && !children.isEmpty()) {
            for (CheckItemConfigTreeVO child : children) {
                String childPath = parentPath + "/" + child.getItemName();
                child.setNodePath(childPath);
                setChildrenNodePath(child, childPath);
            }
        }
    }
    
    /**
     * 递归验证节点，检查循环引用等问题
     */
    private void validateNodeRecursively(CheckItemConfigTreeVO node, Set<String> globalVisited, 
                                       Set<String> currentPath, List<String> issues) {
        if (node == null || ChangnengUtil.isNull(node.getId())) {
            issues.add("发现空节点或节点ID为空");
            return;
        }
        
        String nodeId = node.getId();
        
        // 检查循环引用
        if (currentPath.contains(nodeId)) {
            issues.add("发现循环引用，节点ID: " + nodeId);
            return;
        }
        
        // 检查重复节点
        if (globalVisited.contains(nodeId)) {
            issues.add("发现重复节点，节点ID: " + nodeId);
            return;
        }
        
        globalVisited.add(nodeId);
        currentPath.add(nodeId);
        
        // 递归验证子节点
        List<CheckItemConfigTreeVO> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            for (CheckItemConfigTreeVO child : children) {
                validateNodeRecursively(child, globalVisited, new HashSet<>(currentPath), issues);
            }
        }
    }
}
