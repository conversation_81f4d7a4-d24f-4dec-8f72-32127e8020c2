package org.changneng.framework.frameworkbusiness.service;

import java.util.List;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

/**
 * 环境监管一件事-检查项配置 Service接口
 * 专注于树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface CheckItemConfigService {

    /**
     * 获取完整的树形结构数据
     * 这是核心的树形结构查询方法，将平铺的数据转换为嵌套的树形结构
     *
     * @return 树形结构的检查项配置VO列表，每个节点包含其子节点列表
     */
    List<CheckItemConfigTreeVO> getTreeStructure();

    /**
     * 根据父级ID获取树形结构数据
     *
     * @param parentId 父级ID，如果为null则获取所有顶级节点的树形结构
     * @return 树形结构的检查项配置VO列表
     */
    List<CheckItemConfigTreeVO> getTreeStructureByParentId(String parentId);

    /**
     * 验证树形结构的完整性
     * 检查是否存在循环引用、孤立节点等问题
     *
     * @param treeList 树形结构的检查项配置VO列表
     * @return 验证结果
     */
    ResponseJson validateTreeStructure(List<CheckItemConfigTreeVO> treeList);
}
