package org.changneng.framework.frameworkbusiness.service;

import java.util.List;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

/**
 * 环境监管一件事-检查项配置 Service接口
 * 
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface CheckItemConfigService {
    
    /**
     * 根据主键查询检查项配置
     * 
     * @param id 主键ID
     * @return 检查项配置对象
     */
    CheckItemConfig selectByPrimaryKey(String id);
    
    /**
     * 查询所有检查项配置
     * 
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectAll();
    
    /**
     * 根据父级ID查询子项列表
     * 
     * @param parentId 父级ID
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectByParentId(String parentId);
    
    /**
     * 查询顶级检查项（父级ID为空或null）
     * 
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectTopLevelItems();
    
    /**
     * 根据检查项名称模糊查询
     * 
     * @param itemName 检查项名称
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectByItemNameLike(String itemName);
    
    /**
     * 新增检查项配置
     * 
     * @param checkItemConfig 检查项配置对象
     * @return 操作结果
     */
    ResponseJson insert(CheckItemConfig checkItemConfig);
    
    /**
     * 选择性新增检查项配置（非空字段）
     * 
     * @param checkItemConfig 检查项配置对象
     * @return 操作结果
     */
    ResponseJson insertSelective(CheckItemConfig checkItemConfig);
    
    /**
     * 更新检查项配置
     * 
     * @param checkItemConfig 检查项配置对象
     * @return 操作结果
     */
    ResponseJson updateByPrimaryKey(CheckItemConfig checkItemConfig);
    
    /**
     * 选择性更新检查项配置（非空字段）
     * 
     * @param checkItemConfig 检查项配置对象
     * @return 操作结果
     */
    ResponseJson updateByPrimaryKeySelective(CheckItemConfig checkItemConfig);
    
    /**
     * 根据主键删除检查项配置
     * 
     * @param id 主键ID
     * @return 操作结果
     */
    ResponseJson deleteByPrimaryKey(String id);
    
    /**
     * 根据父级ID批量删除
     * 
     * @param parentId 父级ID
     * @return 操作结果
     */
    ResponseJson deleteByParentId(String parentId);
    
    /**
     * 批量插入检查项配置
     * 
     * @param list 检查项配置列表
     * @return 操作结果
     */
    ResponseJson batchInsert(List<CheckItemConfig> list);
    
    /**
     * 获取指定父级ID下的最大排序值
     * 
     * @param parentId 父级ID
     * @return 最大排序值
     */
    Integer getMaxSortByParentId(String parentId);
    
    /**
     * 更新排序值
     * 
     * @param id 主键ID
     * @param itemSort 新的排序值
     * @return 操作结果
     */
    ResponseJson updateSortById(String id, Integer itemSort);
    
    /**
     * 获取完整的树形结构数据
     * 这是核心的树形结构查询方法，将平铺的数据转换为嵌套的树形结构
     * 
     * @return 树形结构的检查项配置列表，每个节点包含其子节点列表
     */
    List<CheckItemConfig> getTreeStructure();
    
    /**
     * 根据父级ID获取树形结构数据
     * 
     * @param parentId 父级ID，如果为null则获取所有顶级节点的树形结构
     * @return 树形结构的检查项配置列表
     */
    List<CheckItemConfig> getTreeStructureByParentId(String parentId);
    
    /**
     * 构建树形结构（从平铺数据转换为树形结构）
     * 这是一个工具方法，用于将平铺的检查项配置列表转换为树形结构
     * 
     * @param flatList 平铺的检查项配置列表
     * @return 树形结构的检查项配置列表
     */
    List<CheckItemConfig> buildTreeStructure(List<CheckItemConfig> flatList);
    
    /**
     * 验证树形结构的完整性
     * 检查是否存在循环引用、孤立节点等问题
     * 
     * @param treeList 树形结构的检查项配置列表
     * @return 验证结果
     */
    ResponseJson validateTreeStructure(List<CheckItemConfig> treeList);
}
